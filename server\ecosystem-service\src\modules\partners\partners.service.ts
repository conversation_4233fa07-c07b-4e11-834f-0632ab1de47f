import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like } from 'typeorm';
import { Partner, PartnerCertification, PartnerAgreement, PartnerStatus } from '../../entities';
import { CacheService } from '../../common/redis/cache.service';
import { Cacheable } from '../../common/decorators/cache.decorator';

@Injectable()
export class PartnersService {
  private readonly logger = new Logger(PartnersService.name);

  constructor(
    @InjectRepository(Partner)
    private readonly partnerRepository: Repository<Partner>,
    @InjectRepository(PartnerCertification)
    private readonly certificationRepository: Repository<PartnerCertification>,
    @InjectRepository(PartnerAgreement)
    private readonly agreementRepository: Repository<PartnerAgreement>,
    private readonly cacheService: CacheService,
  ) {}

  async getPartners(filters: any = {}) {
    // 尝试从缓存获取
    const cacheKey = this.cacheService.getPartnersListKey(filters);
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryOptions: FindManyOptions<Partner> = {
      relations: ['certifications', 'agreements'],
      order: { createdAt: 'DESC' },
    };

    const where: any = {};

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.tier) {
      where.tier = filters.tier;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.search) {
      where.name = Like(`%${filters.search}%`);
    }

    if (Object.keys(where).length > 0) {
      queryOptions.where = where;
    }

    const [partners, total] = await this.partnerRepository.findAndCount(queryOptions);

    const result = {
      partners,
      total,
      filters
    };

    // 缓存结果（5分钟）
    await this.cacheService.set(cacheKey, result, 300);

    return result;
  }

  async getPartner(id: string) {
    // 尝试从缓存获取
    const cacheKey = this.cacheService.getPartnerKey(id);
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const partner = await this.partnerRepository.findOne({
      where: { partnerId: id },
      relations: ['certifications', 'agreements'],
    });

    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }

    // 缓存结果（10分钟）
    await this.cacheService.set(cacheKey, partner, 600);

    return partner;
  }

  async createApplication(application: any) {
    const partner = this.partnerRepository.create({
      ...application,
      status: PartnerStatus.PENDING,
      joinedAt: new Date(),
      lastActivity: new Date(),
    });

    const savedPartner = await this.partnerRepository.save(partner);

    this.logger.log(`合作伙伴申请创建: ${savedPartner.partnerId} - ${application.name}`);

    return {
      partnerId: savedPartner.partnerId,
      status: 'pending',
      message: '申请已提交，等待审核'
    };
  }

  async updatePartner(id: string, updateData: any) {
    const partner = await this.partnerRepository.findOne({
      where: { partnerId: id },
    });

    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }

    Object.assign(partner, updateData);
    partner.lastActivity = new Date();

    const updatedPartner = await this.partnerRepository.save(partner);

    // 清除相关缓存
    await this.cacheService.del(this.cacheService.getPartnerKey(id));
    await this.cacheService.delPattern('ecosystem:partners:list:*');

    this.logger.log(`合作伙伴更新: ${id}`);

    return updatedPartner;
  }

  async deletePartner(id: string) {
    const partner = await this.partnerRepository.findOne({
      where: { partnerId: id },
    });

    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }

    await this.partnerRepository.remove(partner);

    this.logger.log(`合作伙伴删除: ${id}`);

    return {
      message: '合作伙伴已删除',
      deletedId: id
    };
  }

  async getPartnerPerformance(id: string) {
    const partner = await this.partnerRepository.findOne({
      where: { partnerId: id },
    });

    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }

    return {
      partnerId: id,
      performance: partner.performance || {
        revenue: 0,
        customers: 0,
        projects: 0,
        satisfaction: 0,
        support_rating: 0,
        certification_compliance: 0,
        last_evaluation: new Date()
      },
      trends: {
        revenue_trend: 'stable',
        customer_trend: 'growing',
        satisfaction_trend: 'improving'
      }
    };
  }

  async addCertification(id: string, certification: any) {
    const partner = await this.partnerRepository.findOne({
      where: { partnerId: id },
    });

    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }

    const newCertification = this.certificationRepository.create({
      ...certification,
      partnerId: id,
      validFrom: certification.validFrom || new Date(),
      validTo: certification.validTo || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 默认1年有效期
    });

    const savedCertification = await this.certificationRepository.save(newCertification);

    this.logger.log(`认证添加: ${id} - ${certification.name}`);

    return {
      certificationId: savedCertification.certificationId,
      message: '认证添加成功'
    };
  }
}
