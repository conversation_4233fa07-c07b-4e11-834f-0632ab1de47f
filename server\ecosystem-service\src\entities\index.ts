// 合作伙伴相关实体
export { Partner, PartnerType, PartnerTier, PartnerStatus } from './partner.entity';
export { PartnerCertification, CertificationStatus } from './partner-certification.entity';
export { PartnerAgreement, AgreementType, AgreementStatus } from './partner-agreement.entity';

// API相关实体
export { ApiSpecification, APIType, APIStatus } from './api-specification.entity';
export { ApiUsageRecord } from './api-usage-record.entity';

// 第三方应用相关实体
export { ThirdPartyApplication, ApplicationType, ApplicationStatus, PricingType } from './third-party-application.entity';
export { ApplicationReview } from './application-review.entity';

// 行业标准相关实体
export { IndustryStandard, StandardStatus } from './industry-standard.entity';
export { StandardCertification, CertificationApplicationStatus } from './standard-certification.entity';
export { ComplianceRecord } from './compliance-record.entity';

// 所有实体的数组，用于TypeORM配置
export const entities = [
  // 合作伙伴相关
  Partner,
  PartnerCertification,
  PartnerAgreement,
  
  // API相关
  ApiSpecification,
  ApiUsageRecord,
  
  // 第三方应用相关
  ThirdPartyApplication,
  ApplicationReview,
  
  // 行业标准相关
  IndustryStandard,
  StandardCertification,
  ComplianceRecord,
];
