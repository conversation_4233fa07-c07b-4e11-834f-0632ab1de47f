import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { PartnersService } from './partners.service';
import { Roles } from '../../common/auth/decorators/roles.decorator';
import { Permissions } from '../../common/auth/decorators/permissions.decorator';
import { CurrentUser } from '../../common/auth/decorators/current-user.decorator';
import { RateLimit } from '../../common/decorators/rate-limit.decorator';
import { User } from '../../common/auth/auth.service';

@ApiTags('partners')
@ApiBearerAuth()
@Controller('partners')
export class PartnersController {
  constructor(private readonly partnersService: PartnersService) {}

  @Get()
  @Permissions('partners:read')
  @RateLimit({ requests: 100, window: 60 })
  @ApiOperation({ summary: '获取合作伙伴列表' })
  @ApiResponse({ status: 200, description: '合作伙伴列表' })
  @ApiQuery({ name: 'type', required: false, description: '合作伙伴类型' })
  @ApiQuery({ name: 'tier', required: false, description: '合作伙伴等级' })
  @ApiQuery({ name: 'status', required: false, description: '合作伙伴状态' })
  async getPartners(
    @Query('type') type?: string,
    @Query('tier') tier?: string,
    @Query('status') status?: string,
    @CurrentUser() user: User,
  ) {
    return this.partnersService.getPartners({ type, tier, status });
  }

  @Get(':id')
  @Permissions('partners:read')
  @RateLimit({ requests: 200, window: 60 })
  @ApiOperation({ summary: '获取合作伙伴详情' })
  @ApiResponse({ status: 200, description: '合作伙伴详情' })
  @ApiParam({ name: 'id', description: '合作伙伴ID' })
  async getPartner(@Param('id') id: string, @CurrentUser() user: User) {
    return this.partnersService.getPartner(id);
  }

  @Post()
  @Permissions('partners:create')
  @RateLimit({ requests: 10, window: 60 })
  @ApiOperation({ summary: '创建合作伙伴申请' })
  @ApiResponse({ status: 201, description: '申请创建成功' })
  async createPartnerApplication(@Body() application: any, @CurrentUser() user: User) {
    return this.partnersService.createApplication(application);
  }

  @Put(':id')
  @Permissions('partners:update')
  @RateLimit({ requests: 20, window: 60 })
  @ApiOperation({ summary: '更新合作伙伴信息' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiParam({ name: 'id', description: '合作伙伴ID' })
  async updatePartner(@Param('id') id: string, @Body() updateData: any, @CurrentUser() user: User) {
    return this.partnersService.updatePartner(id, updateData);
  }

  @Delete(':id')
  @Roles('admin', 'partner_manager')
  @RateLimit({ requests: 5, window: 60 })
  @ApiOperation({ summary: '删除合作伙伴' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiParam({ name: 'id', description: '合作伙伴ID' })
  async deletePartner(@Param('id') id: string, @CurrentUser() user: User) {
    return this.partnersService.deletePartner(id);
  }

  @Get(':id/performance')
  @ApiOperation({ summary: '获取合作伙伴性能指标' })
  @ApiResponse({ status: 200, description: '性能指标' })
  @ApiParam({ name: 'id', description: '合作伙伴ID' })
  async getPartnerPerformance(@Param('id') id: string) {
    return this.partnersService.getPartnerPerformance(id);
  }

  @Post(':id/certifications')
  @ApiOperation({ summary: '添加合作伙伴认证' })
  @ApiResponse({ status: 201, description: '认证添加成功' })
  @ApiParam({ name: 'id', description: '合作伙伴ID' })
  async addCertification(@Param('id') id: string, @Body() certification: any) {
    return this.partnersService.addCertification(id, certification);
  }
}
